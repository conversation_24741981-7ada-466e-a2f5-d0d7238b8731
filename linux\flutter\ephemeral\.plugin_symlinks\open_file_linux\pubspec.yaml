name: open_file_linux
description: Linux platform implementation of open_file
repository: https://github.com/crazecoder/open_file/tree/master/open_file_linux
version: 0.0.5

environment:
  sdk: ">=2.17.0 <4.0.0"
  flutter: ">=1.20.0"

dependencies:
  flutter:
    sdk: flutter
  ffi: ^2.1.3
  open_file_platform_interface: ^1.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' specifies the class (in Java, Kotlin, Swift, Objective-C, etc.)
  # which should be registered in the plugin registry. This is required for
  # using method channels.
  # The Android 'package' specifies package in which the registered class is.
  # This is required for using method channels on Android.
  # The 'ffiPlugin' specifies that native code should be built and bundled.
  # This is required for using `dart:ffi`.
  # All these are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    implements: open_file
    platforms:
      linux:
        dartPluginClass: OpenFileLinux
        pluginClass: OpenFileLinuxPlugin