import 'package:flutter/material.dart';
import '../models/chat_message.dart';
import '../services/ai_chat_service.dart';
import 'package:uuid/uuid.dart';

class AIChatProvider extends ChangeNotifier {
  static const _uuid = Uuid();
  
  List<ChatMessage> _messages = [];
  bool _isTyping = false;
  bool _isInitialized = false;
  String _currentLanguage = 'en';

  List<ChatMessage> get messages => List.unmodifiable(_messages);
  bool get isTyping => _isTyping;
  bool get isInitialized => _isInitialized;

  void setLanguage(String language) {
    _currentLanguage = language;
    if (_isInitialized) {
      _resetChat();
    }
  }

  void initializeChat() {
    if (!_isInitialized) {
      _messages.clear();
      _messages.add(AIChatService.getWelcomeMessage(_currentLanguage));
      _isInitialized = true;
      notifyListeners();
    }
  }

  Future<void> sendMessage(String text) async {
    if (text.trim().isEmpty) return;

    // Add user message
    final userMessage = ChatMessage(
      id: _uuid.v4(),
      text: text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );
    
    _messages.add(userMessage);
    notifyListeners();

    // Show typing indicator
    _showTypingIndicator();

    try {
      // Generate AI response
      final botResponse = await AIChatService.generateAIResponse(text, _currentLanguage);
      
      _hideTypingIndicator();
      _messages.add(botResponse);
      notifyListeners();
    } catch (e) {
      _hideTypingIndicator();
      // Add error message
      final errorMessage = ChatMessage(
        id: _uuid.v4(),
        text: _getErrorMessage(),
        isUser: false,
        timestamp: DateTime.now(),
        type: MessageType.text,
      );
      _messages.add(errorMessage);
      notifyListeners();
    }
  }

  void sendQuickReply(String text) {
    sendMessage(text);
  }

  void _showTypingIndicator() {
    _isTyping = true;
    final typingMessage = AIChatService.createTypingMessage();
    _messages.add(typingMessage);
    notifyListeners();
  }

  void _hideTypingIndicator() {
    _isTyping = false;
    // Remove typing message
    _messages.removeWhere((message) => message.type == MessageType.typing);
    notifyListeners();
  }

  void _resetChat() {
    _messages.clear();
    _isInitialized = false;
    _isTyping = false;
    initializeChat();
  }

  void clearChat() {
    _messages.clear();
    _isTyping = false;
    _isInitialized = false;
    notifyListeners();
  }

  void resetChat() {
    _resetChat();
  }

  String _getErrorMessage() {
    switch (_currentLanguage) {
      case 'fr':
        return 'Désolé, je rencontre des difficultés techniques. Veuillez réessayer.';
      case 'ar':
        return 'عذراً، أواجه صعوبات تقنية. يرجى المحاولة مرة أخرى.';
      default:
        return 'Sorry, I\'m experiencing technical difficulties. Please try again.';
    }
  }
}
