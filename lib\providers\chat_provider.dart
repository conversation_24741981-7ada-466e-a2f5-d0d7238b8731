import 'package:flutter/material.dart';
import '../models/chat_message_model.dart';
import '../services/chat_service.dart';
import 'package:uuid/uuid.dart';

class ChatProvider extends ChangeNotifier {
  static const _uuid = Uuid();
  
  List<ChatMessage> _messages = [];
  bool _isTyping = false;
  bool _isInitialized = false;

  List<ChatMessage> get messages => List.unmodifiable(_messages);
  bool get isTyping => _isTyping;
  bool get isInitialized => _isInitialized;

  void initializeChat(BuildContext context) {
    if (!_isInitialized) {
      _messages.clear();
      _messages.add(ChatService.getWelcomeMessage(context));
      _isInitialized = true;
      notifyListeners();
    }
  }

  void sendMessage(String text, BuildContext context) {
    if (text.trim().isEmpty) return;

    // Add user message
    final userMessage = ChatMessage(
      id: _uuid.v4(),
      text: text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );
    
    _messages.add(userMessage);
    notifyListeners();

    // Show typing indicator
    _showTypingIndicator();

    // Generate bot response after a delay
    Future.delayed(const Duration(milliseconds: 1500), () {
      _hideTypingIndicator();
      
      final botResponse = ChatService.generateBotResponse(text, context);
      _messages.add(botResponse);
      notifyListeners();
    });
  }

  void sendQuickReply(String text, BuildContext context) {
    sendMessage(text, context);
  }

  void _showTypingIndicator() {
    _isTyping = true;
    final typingMessage = ChatService.createTypingMessage();
    _messages.add(typingMessage);
    notifyListeners();
  }

  void _hideTypingIndicator() {
    _isTyping = false;
    // Remove typing message
    _messages.removeWhere((message) => message.type == MessageType.typing);
    notifyListeners();
  }

  void clearChat() {
    _messages.clear();
    _isTyping = false;
    _isInitialized = false;
    notifyListeners();
  }

  void resetChat(BuildContext context) {
    clearChat();
    initializeChat(context);
  }
}
