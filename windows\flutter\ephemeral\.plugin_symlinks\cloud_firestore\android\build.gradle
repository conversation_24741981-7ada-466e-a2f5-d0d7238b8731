group 'io.flutter.plugins.firebase.cloudfirestore'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

def firebaseCoreProject = findProject(':firebase_core')
if (firebaseCoreProject == null) {
    throw new GradleException('Could not find the firebase_core FlutterFire plugin, have you added it as a dependency in your pubspec?')
} else if (!firebaseCoreProject.properties['FirebaseSDKVersion']) {
    throw new GradleException('A newer version of the firebase_core FlutterFire plugin is required, please update your firebase_core pubspec dependency.')
}

def getRootProjectExtOrCoreProperty(name, firebaseCoreProject) {
    if (!rootProject.ext.has('FlutterFire')) return firebaseCoreProject.properties[name]
    if (!rootProject.ext.get('FlutterFire')[name]) return firebaseCoreProject.properties[name]
    return rootProject.ext.get('FlutterFire').get(name)
}

android {
    // Conditional for compatibility with AGP <4.2.
    if (project.android.hasProperty("namespace")) {
      namespace 'io.flutter.plugins.firebase.firestore'
    }

    compileSdk 34

    defaultConfig {
        minSdk 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    compileOptions {
      sourceCompatibility JavaVersion.VERSION_1_8
      targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
      buildConfig true
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    dependencies {
        api firebaseCoreProject
        implementation platform("com.google.firebase:firebase-bom:${getRootProjectExtOrCoreProperty("FirebaseSDKVersion", firebaseCoreProject)}")
        implementation 'com.google.firebase:firebase-firestore'
    }
}

apply from: file("./user-agent.gradle")
