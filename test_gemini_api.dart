import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  await testGeminiAPI();
}

Future<void> testGeminiAPI() async {
  const String apiKey = 'AIzaSyB70K4h6pR6QrPVpT4FMG4VfrQjb4_W6_I';
  const String baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
  
  print('🤖 Testing Gemini API...');
  
  final requestBody = {
    'contents': [
      {
        'parts': [
          {
            'text': 'Tell me about <PERSON>rd<PERSON> in Algeria in 2 sentences.'
          }
        ]
      }
    ],
    'generationConfig': {
      'temperature': 0.7,
      'topK': 40,
      'topP': 0.95,
      'maxOutputTokens': 1024,
    },
  };

  try {
    final response = await http.post(
      Uri.parse('$baseUrl?key=$apiKey'),
      headers: {
        'Content-Type': 'application/json',
      },
      body: json.encode(requestBody),
    );

    print('🤖 Response Status: ${response.statusCode}');
    
    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      print('🤖 Full Response: $responseData');
      
      if (responseData['candidates'] != null && 
          responseData['candidates'].isNotEmpty &&
          responseData['candidates'][0]['content'] != null &&
          responseData['candidates'][0]['content']['parts'] != null &&
          responseData['candidates'][0]['content']['parts'].isNotEmpty) {
        
        String aiResponse = responseData['candidates'][0]['content']['parts'][0]['text'];
        print('🤖 AI Response: $aiResponse');
        print('✅ API Test Successful!');
      } else {
        print('❌ Invalid response structure');
      }
    } else {
      print('❌ API Error: ${response.statusCode} - ${response.body}');
    }
  } catch (e) {
    print('❌ Error: $e');
  }
}
