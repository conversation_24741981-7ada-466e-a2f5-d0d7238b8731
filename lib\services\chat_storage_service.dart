import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_message.dart';

class ChatStorageService {
  static const String _lastPromptsKey = 'ai_chat_last_prompts';
  static const int _maxStoredPrompts = 3;

  /// Save the last 3 prompts (user messages) to local storage
  static Future<void> saveLastPrompts(List<ChatMessage> allMessages) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Filter only user messages (prompts)
      final userMessages = allMessages
          .where((message) => message.isUser)
          .toList();
      
      // Take only the last 3 prompts
      final lastPrompts = userMessages.length > _maxStoredPrompts
          ? userMessages.sublist(userMessages.length - _maxStoredPrompts)
          : userMessages;
      
      // Convert to JSON
      final promptsJson = lastPrompts
          .map((message) => message.toJson())
          .toList();
      
      // Save to SharedPreferences
      await prefs.setString(_lastPromptsKey, json.encode(promptsJson));
      
      print('💾 Saved ${lastPrompts.length} prompts to storage');
    } catch (e) {
      print('❌ Error saving prompts: $e');
    }
  }

  /// Load the last 3 prompts from local storage
  static Future<List<ChatMessage>> loadLastPrompts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final promptsString = prefs.getString(_lastPromptsKey);
      
      if (promptsString == null || promptsString.isEmpty) {
        print('📭 No saved prompts found');
        return [];
      }
      
      final promptsJson = json.decode(promptsString) as List;
      final prompts = promptsJson
          .map((json) => ChatMessage.fromJson(json))
          .toList();
      
      print('📥 Loaded ${prompts.length} prompts from storage');
      return prompts;
    } catch (e) {
      print('❌ Error loading prompts: $e');
      return [];
    }
  }

  /// Get the last 3 prompts as text strings for display
  static Future<List<String>> getLastPromptsAsText() async {
    final prompts = await loadLastPrompts();
    return prompts.map((prompt) => prompt.text).toList();
  }

  /// Clear all saved prompts
  static Future<void> clearSavedPrompts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastPromptsKey);
      print('🗑️ Cleared all saved prompts');
    } catch (e) {
      print('❌ Error clearing prompts: $e');
    }
  }

  /// Check if there are any saved prompts
  static Future<bool> hasSavedPrompts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final promptsString = prefs.getString(_lastPromptsKey);
      return promptsString != null && promptsString.isNotEmpty;
    } catch (e) {
      print('❌ Error checking saved prompts: $e');
      return false;
    }
  }

  /// Get saved prompts count
  static Future<int> getSavedPromptsCount() async {
    final prompts = await loadLastPrompts();
    return prompts.length;
  }

  /// Add a single prompt to saved prompts (maintains max 3)
  static Future<void> addPromptToSaved(ChatMessage userMessage) async {
    if (!userMessage.isUser) return;
    
    try {
      final existingPrompts = await loadLastPrompts();
      existingPrompts.add(userMessage);
      
      // Keep only last 3
      final lastThree = existingPrompts.length > _maxStoredPrompts
          ? existingPrompts.sublist(existingPrompts.length - _maxStoredPrompts)
          : existingPrompts;
      
      final prefs = await SharedPreferences.getInstance();
      final promptsJson = lastThree
          .map((message) => message.toJson())
          .toList();
      
      await prefs.setString(_lastPromptsKey, json.encode(promptsJson));
      print('💾 Added prompt to saved list (${lastThree.length}/3)');
    } catch (e) {
      print('❌ Error adding prompt: $e');
    }
  }
}
