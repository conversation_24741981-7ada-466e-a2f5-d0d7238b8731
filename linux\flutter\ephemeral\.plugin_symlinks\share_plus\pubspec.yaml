name: share_plus
description: Flutter plugin for sharing content via the platform share UI, using the ACTION_SEND intent on Android and UIActivityViewController on iOS.
version: 7.2.2
homepage: https://plus.fluttercommunity.dev/
repository: https://github.com/fluttercommunity/plus_plugins/tree/main/packages/share_plus/share_plus
issue_tracker: https://github.com/fluttercommunity/plus_plugins/labels/share_plus

flutter:
  plugin:
    platforms:
      android:
        package: dev.fluttercommunity.plus.share
        pluginClass: SharePlusPlugin
      ios:
        pluginClass: FPPSharePlusPlugin
      linux:
        dartPluginClass: SharePlusLinuxPlugin
      macos:
        pluginClass: SharePlusMacosPlugin
      web:
        pluginClass: SharePlusWebPlugin
        fileName: src/share_plus_web.dart
      windows:
        dartPluginClass: SharePlusWindowsPlugin
        pluginClass: SharePlusWindowsPluginCApi

dependencies:
  cross_file: ^0.3.3+4
  meta: ^1.8.0
  mime: ^1.0.4
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  share_plus_platform_interface: ^3.3.1
  file: ">=6.1.4 <8.0.0"
  url_launcher_web: ^2.0.16
  url_launcher_windows: ^3.0.6
  url_launcher_linux: ^3.0.5
  url_launcher_platform_interface: ^2.1.2
  ffi: ^2.0.1

  # win32 is compatible across v4 and v5 for Win32 only (not COM)
  win32: ">=4.0.0 <6.0.0"

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ">=2.0.1 <4.0.0"

environment:
  sdk: ">=2.18.0 <4.0.0"
  flutter: ">=3.3.0"
