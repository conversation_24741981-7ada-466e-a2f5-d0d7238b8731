import '../models/chat_message_model.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'gemini_service.dart';

class ChatService {
  static const _uuid = Uuid();

  static Future<ChatMessage> generateBotResponse(String userMessage, BuildContext context) async {
    try {
      final aiResponse = await GeminiService.generateResponse(userMessage, context);

      return ChatMessage(
        id: _uuid.v4(),
        text: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
        type: MessageType.quickReply,
        quickReplies: GeminiService.getQuickReplies(context),
      );
    } catch (e) {
      // Fallback to a default response if AI fails
      return ChatMessage(
        id: _uuid.v4(),
        text: _getFallbackResponse(context),
        isUser: false,
        timestamp: DateTime.now(),
        type: MessageType.quickReply,
        quickReplies: GeminiService.getQuickReplies(context),
      );
    }
  }

  static String _getFallbackResponse(BuildContext context) {
    final language = Localizations.localeOf(context).languageCode;

    switch (language) {
      case 'fr':
        return 'Je suis désolé, je rencontre des difficultés techniques. Puis-je vous aider avec des informations sur Bordj El Mokrani ?';
      case 'ar':
        return 'أعتذر، أواجه صعوبات تقنية. هل يمكنني مساعدتك بمعلومات حول برج المقراني؟';
      default:
        return 'I apologize, I\'m experiencing technical difficulties. Can I help you with information about Bordj El Mokrani?';
    }
  }

  static ChatMessage getWelcomeMessage(BuildContext context) {
    return ChatMessage(
      id: _uuid.v4(),
      text: GeminiService.getWelcomeMessage(context),
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.quickReply,
      quickReplies: GeminiService.getQuickReplies(context),
    );
  }

  static ChatMessage createTypingMessage() {
    return ChatMessage(
      id: _uuid.v4(),
      text: '...',
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.typing,
    );
  }
}
