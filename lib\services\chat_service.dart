import 'dart:math';
import '../models/chat_message_model.dart';
import '../extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

class ChatService {
  static const _uuid = Uuid();
  static final Random _random = Random();

  static List<Map<String, dynamic>> _getBotResponses(BuildContext context) {
    return [
      {
        'keywords': ['hello', 'hi', 'hey', 'bonjour', 'salut', 'مرحبا', 'أهلا'],
        'responses': [
          'chat_welcome'.tr(context),
          'chat_greeting'.tr(context),
          'chat_hello_response'.tr(context),
        ],
        'quickReplies': [
          'chat_ask_history'.tr(context),
          'chat_ask_visit'.tr(context),
          'chat_ask_location'.tr(context),
        ],
      },
      {
        'keywords': ['history', 'historical', 'past', 'histoire', 'historique', 'تاريخ', 'تاريخي'],
        'responses': [
          'chat_history_response'.tr(context),
          'chat_fortress_history'.tr(context),
        ],
        'quickReplies': [
          'chat_ask_mokrani'.tr(context),
          'chat_ask_ottoman'.tr(context),
          'chat_ask_more_info'.tr(context),
        ],
      },
      {
        'keywords': ['visit', 'tour', 'booking', 'reservation', 'visite', 'réservation', 'زيارة', 'حجز'],
        'responses': [
          'chat_visit_response'.tr(context),
          'chat_booking_info'.tr(context),
        ],
        'quickReplies': [
          'chat_opening_hours'.tr(context),
          'chat_ticket_price'.tr(context),
          'chat_book_now'.tr(context),
        ],
      },
      {
        'keywords': ['location', 'where', 'address', 'lieu', 'adresse', 'موقع', 'عنوان'],
        'responses': [
          'chat_location_response'.tr(context),
        ],
        'quickReplies': [
          'chat_directions'.tr(context),
          'chat_transport'.tr(context),
          'chat_nearby'.tr(context),
        ],
      },
      {
        'keywords': ['mokrani', 'cheikh', 'revolt', 'resistance', 'révolte', 'résistance', 'مقراني', 'ثورة'],
        'responses': [
          'chat_mokrani_response'.tr(context),
          'chat_revolt_info'.tr(context),
        ],
        'quickReplies': [
          'chat_ask_history'.tr(context),
          'chat_ask_visit'.tr(context),
          'chat_learn_more'.tr(context),
        ],
      },
      {
        'keywords': ['ottoman', 'turkish', 'fortress', 'bordj', 'ottoman', 'turc', 'عثماني', 'تركي', 'برج'],
        'responses': [
          'chat_ottoman_response'.tr(context),
        ],
        'quickReplies': [
          'chat_ask_architecture'.tr(context),
          'chat_ask_history'.tr(context),
          'chat_ask_visit'.tr(context),
        ],
      },
      {
        'keywords': ['hours', 'open', 'time', 'schedule', 'horaires', 'ouvert', 'مواعيد', 'ساعات'],
        'responses': [
          'chat_hours_response'.tr(context),
        ],
        'quickReplies': [
          'chat_book_now'.tr(context),
          'chat_ticket_price'.tr(context),
          'chat_ask_visit'.tr(context),
        ],
      },
      {
        'keywords': ['price', 'cost', 'ticket', 'fee', 'prix', 'coût', 'billet', 'سعر', 'تذكرة'],
        'responses': [
          'chat_price_response'.tr(context),
        ],
        'quickReplies': [
          'chat_book_now'.tr(context),
          'chat_group_discount'.tr(context),
          'chat_ask_visit'.tr(context),
        ],
      },
      {
        'keywords': ['help', 'assistance', 'support', 'aide', 'assistance', 'مساعدة', 'دعم'],
        'responses': [
          'chat_help_response'.tr(context),
        ],
        'quickReplies': [
          'chat_ask_history'.tr(context),
          'chat_ask_visit'.tr(context),
          'chat_contact_info'.tr(context),
        ],
      },
    ];
  }

  static ChatMessage generateBotResponse(String userMessage, BuildContext context) {
    final responses = _getBotResponses(context);
    final userMessageLower = userMessage.toLowerCase();

    // Find matching response based on keywords
    for (final responseData in responses) {
      final keywords = responseData['keywords'] as List<String>;
      if (keywords.any((keyword) => userMessageLower.contains(keyword.toLowerCase()))) {
        final responseTexts = responseData['responses'] as List<String>;
        final selectedResponse = responseTexts[_random.nextInt(responseTexts.length)];
        final quickReplies = responseData['quickReplies'] as List<String>?;

        return ChatMessage(
          id: _uuid.v4(),
          text: selectedResponse,
          isUser: false,
          timestamp: DateTime.now(),
          type: quickReplies != null ? MessageType.quickReply : MessageType.text,
          quickReplies: quickReplies,
        );
      }
    }

    // Default response if no keywords match
    final defaultResponses = [
      'chat_default_response'.tr(context),
      'chat_ask_clarification'.tr(context),
      'chat_suggest_topics'.tr(context),
    ];

    return ChatMessage(
      id: _uuid.v4(),
      text: defaultResponses[_random.nextInt(defaultResponses.length)],
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.quickReply,
      quickReplies: [
        'chat_ask_history'.tr(context),
        'chat_ask_visit'.tr(context),
        'chat_ask_location'.tr(context),
      ],
    );
  }

  static ChatMessage getWelcomeMessage(BuildContext context) {
    return ChatMessage(
      id: _uuid.v4(),
      text: 'chat_welcome_message'.tr(context),
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.quickReply,
      quickReplies: [
        'chat_ask_history'.tr(context),
        'chat_ask_visit'.tr(context),
        'chat_ask_location'.tr(context),
      ],
    );
  }

  static ChatMessage createTypingMessage() {
    return ChatMessage(
      id: _uuid.v4(),
      text: '...',
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.typing,
    );
  }
}
