import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import '../extensions/string_extensions.dart';

class GeminiService {
  static const String _apiKey = 'AIzaSyB70K4h6pR6QrPVpT4FMG4VfrQjb4_W6_I';
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

  static String _getSystemPrompt(BuildContext context) {
    final language = Localizations.localeOf(context).languageCode;
    
    String languageInstruction = '';
    switch (language) {
      case 'fr':
        languageInstruction = 'Respond in French.';
        break;
      case 'ar':
        languageInstruction = 'Respond in Arabic.';
        break;
      default:
        languageInstruction = 'Respond in English.';
    }

    return '''
You are a knowledgeable and friendly virtual guide for Bordj El Mokrani, a historic fortress in Algeria. Your role is to help visitors learn about the history, plan their visits, and answer questions about this magnificent site.

Key Information about Bordj <PERSON>:
- Built during the Ottoman era around 1774
- Located in Bordj Bou Arreridj Province, Algeria
- Associated with <PERSON><PERSON><PERSON>, who led the famous Mokrani Revolt in 1871 against French colonial rule
- The fortress served as a military and administrative center during Ottoman rule
- Open Saturday through Thursday from 8:00 AM to 5:00 PM (closed Fridays)
- Offers guided tours, audio guides, and interactive exhibits
- Entry fees are affordable with special rates for students, groups, and families
- Reservations recommended 24 hours in advance

Historical Context:
- Pre-Islamic Era: Originally inhabited by Berber tribes, part of ancient Numidia
- Roman Period (146 BCE - 430 CE): Part of Roman province of Numidia
- Islamic Period (7th-8th Century): Came under Islamic influence during Arab conquest
- Ottoman Period (16th-19th Century): Fortress built as military outpost, gave area its name
- Mokrani Period (19th Century): Stronghold of Cheikh El Mokrani's resistance
- French Colonial Period (1830-1962): Continued resistance, played role in independence war
- Independence Era (1962-Present): Developed as regional center, became province in 1984
- Modern Period: Industrial and commercial hub, especially electronics industry

Your personality:
- Enthusiastic and passionate about the history
- Helpful and informative
- Culturally sensitive and respectful
- Encouraging visitors to explore and learn
- Professional but warm and approachable

Guidelines:
- Keep responses concise but informative (2-3 sentences typically)
- Provide specific details when asked
- Suggest related topics or questions when appropriate
- Always be encouraging about visiting
- If asked about topics outside Bordj El Mokrani, politely redirect to the fortress
- $languageInstruction
- Use appropriate cultural greetings and expressions for the language

Remember: You are representing this historic site, so maintain dignity and respect for its cultural significance while being engaging and helpful.
''';
  }

  static Future<String> generateResponse(String userMessage, BuildContext context) async {
    try {
      final systemPrompt = _getSystemPrompt(context);
      
      final requestBody = {
        'contents': [
          {
            'parts': [
              {
                'text': '$systemPrompt\n\nUser: $userMessage\n\nAssistant:'
              }
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.7,
          'topK': 40,
          'topP': 0.95,
          'maxOutputTokens': 1024,
        },
        'safetySettings': [
          {
            'category': 'HARM_CATEGORY_HARASSMENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_HATE_SPEECH',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      };

      final response = await http.post(
        Uri.parse('$_baseUrl?key=$_apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        
        if (responseData['candidates'] != null && 
            responseData['candidates'].isNotEmpty &&
            responseData['candidates'][0]['content'] != null &&
            responseData['candidates'][0]['content']['parts'] != null &&
            responseData['candidates'][0]['content']['parts'].isNotEmpty) {
          
          String aiResponse = responseData['candidates'][0]['content']['parts'][0]['text'];
          
          // Clean up the response
          aiResponse = aiResponse.trim();
          if (aiResponse.startsWith('Assistant:')) {
            aiResponse = aiResponse.substring(10).trim();
          }
          
          return aiResponse;
        } else {
          return _getFallbackResponse(context);
        }
      } else {
        debugPrint('Gemini API Error: ${response.statusCode} - ${response.body}');
        return _getFallbackResponse(context);
      }
    } catch (e) {
      debugPrint('Error calling Gemini API: $e');
      return _getFallbackResponse(context);
    }
  }

  static String _getFallbackResponse(BuildContext context) {
    final language = Localizations.localeOf(context).languageCode;
    
    switch (language) {
      case 'fr':
        return 'Je suis désolé, je rencontre des difficultés techniques en ce moment. Puis-je vous aider avec des informations sur Bordj El Mokrani ? Nous sommes ouverts du samedi au jeudi de 8h00 à 17h00.';
      case 'ar':
        return 'أعتذر، أواجه صعوبات تقنية في الوقت الحالي. هل يمكنني مساعدتك بمعلومات حول برج المقراني؟ نحن مفتوحون من السبت إلى الخميس من 8:00 صباحاً إلى 5:00 مساءً.';
      default:
        return 'I apologize, I\'m experiencing some technical difficulties right now. Can I help you with information about Bordj El Mokrani? We\'re open Saturday through Thursday from 8:00 AM to 5:00 PM.';
    }
  }

  static List<String> getQuickReplies(BuildContext context) {
    return [
      'chat_ask_history'.tr(context),
      'chat_ask_visit'.tr(context),
      'chat_ask_location'.tr(context),
    ];
  }

  static String getWelcomeMessage(BuildContext context) {
    final language = Localizations.localeOf(context).languageCode;
    
    switch (language) {
      case 'fr':
        return 'Bienvenue à Bordj El Mokrani ! 🏰 Je suis votre guide virtuel alimenté par l\'IA. Je peux vous aider à découvrir notre riche histoire, planifier votre visite, ou répondre à toutes vos questions sur cette magnifique forteresse. Comment puis-je vous aider aujourd\'hui ?';
      case 'ar':
        return 'مرحباً بكم في برج المقراني ! 🏰 أنا دليلكم الافتراضي المدعوم بالذكاء الاصطناعي. يمكنني مساعدتكم في اكتشاف تاريخنا الغني، تخطيط زيارتكم، أو الإجابة على أي أسئلة حول هذه القلعة الرائعة. كيف يمكنني مساعدتكم اليوم؟';
      default:
        return 'Welcome to Bordj El Mokrani! 🏰 I\'m your AI-powered virtual guide. I can help you discover our rich history, plan your visit, or answer any questions about this magnificent fortress. How can I help you today?';
    }
  }
}
