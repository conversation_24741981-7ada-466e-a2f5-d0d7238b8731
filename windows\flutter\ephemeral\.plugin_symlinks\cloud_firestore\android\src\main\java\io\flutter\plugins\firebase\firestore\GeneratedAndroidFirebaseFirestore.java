// Copyright 2023, the Chromium project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.
// Autogenerated from Pigeon (v11.0.1), do not edit directly.
// See also: https://pub.dev/packages/pigeon

package io.flutter.plugins.firebase.firestore;

import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.flutter.plugin.common.BasicMessageChannel;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MessageCodec;
import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** Generated class from Pigeon. */
@SuppressWarnings({"unused", "unchecked", "CodeBlock2Expr", "RedundantSuppression", "serial"})
public class GeneratedAndroidFirebaseFirestore {

  /** Error class for passing custom error details to <PERSON>lutter via a thrown PlatformException. */
  public static class FlutterError extends RuntimeException {

    /** The error code. */
    public final String code;

    /** The error details. Must be a datatype supported by the api codec. */
    public final Object details;

    public FlutterError(@NonNull String code, @Nullable String message, @Nullable Object details) {
      super(message);
      this.code = code;
      this.details = details;
    }
  }

  @NonNull
  protected static ArrayList<Object> wrapError(@NonNull Throwable exception) {
    ArrayList<Object> errorList = new ArrayList<Object>(3);
    if (exception instanceof FlutterError) {
      FlutterError error = (FlutterError) exception;
      errorList.add(error.code);
      errorList.add(error.getMessage());
      errorList.add(error.details);
    } else {
      errorList.add(exception.toString());
      errorList.add(exception.getClass().getSimpleName());
      errorList.add(
          "Cause: " + exception.getCause() + ", Stacktrace: " + Log.getStackTraceString(exception));
    }
    return errorList;
  }

  /** An enumeration of document change types. */
  public enum DocumentChangeType {
    /** Indicates a new document was added to the set of documents matching the query. */
    ADDED(0),
    /** Indicates a document within the query was modified. */
    MODIFIED(1),
    /**
     * Indicates a document within the query was removed (either deleted or no longer matches the
     * query.
     */
    REMOVED(2);

    final int index;

    private DocumentChangeType(final int index) {
      this.index = index;
    }
  }

  /** An enumeration of firestore source types. */
  public enum Source {
    /**
     * Causes Firestore to try to retrieve an up-to-date (server-retrieved) snapshot, but fall back
     * to returning cached data if the server can't be reached.
     */
    SERVER_AND_CACHE(0),
    /**
     * Causes Firestore to avoid the cache, generating an error if the server cannot be reached.
     * Note that the cache will still be updated if the server request succeeds. Also note that
     * latency-compensation still takes effect, so any pending write operations will be visible in
     * the returned data (merged into the server-provided data).
     */
    SERVER(1),
    /**
     * Causes Firestore to immediately return a value from the cache, ignoring the server completely
     * (implying that the returned value may be stale with respect to the value on the server). If
     * there is no data in the cache to satisfy the `get` call, [DocumentReference.get] will throw a
     * [FirebaseException] and [Query.get] will return an empty [QuerySnapshotPlatform] with no
     * documents.
     */
    CACHE(2);

    final int index;

    private Source(final int index) {
      this.index = index;
    }
  }

  /**
   * The listener retrieves data and listens to updates from the local Firestore cache only. If the
   * cache is empty, an empty snapshot will be returned. Snapshot events will be triggered on cache
   * updates, like local mutations or load bundles.
   *
   * <p>Note that the data might be stale if the cache hasn't synchronized with recent server-side
   * changes.
   */
  public enum ListenSource {
    /**
     * The default behavior. The listener attempts to return initial snapshot from cache and
     * retrieve up-to-date snapshots from the Firestore server. Snapshot events will be triggered on
     * local mutations and server side updates.
     */
    DEFAULT_SOURCE(0),
    /**
     * The listener retrieves data and listens to updates from the local Firestore cache only. If
     * the cache is empty, an empty snapshot will be returned. Snapshot events will be triggered on
     * cache updates, like local mutations or load bundles.
     */
    CACHE(1);

    final int index;

    private ListenSource(final int index) {
      this.index = index;
    }
  }

  public enum ServerTimestampBehavior {
    /** Return null for [FieldValue.serverTimestamp()] values that have not yet */
    NONE(0),
    /**
     * Return local estimates for [FieldValue.serverTimestamp()] values that have not yet been set
     * to their final value.
     */
    ESTIMATE(1),
    /**
     * Return the previous value for [FieldValue.serverTimestamp()] values that have not yet been
     * set to their final value.
     */
    PREVIOUS(2);

    final int index;

    private ServerTimestampBehavior(final int index) {
      this.index = index;
    }
  }

  /** [AggregateSource] represents the source of data for an [AggregateQuery]. */
  public enum AggregateSource {
    /** Indicates that the data should be retrieved from the server. */
    SERVER(0);

    final int index;

    private AggregateSource(final int index) {
      this.index = index;
    }
  }

  public enum PigeonTransactionResult {
    SUCCESS(0),
    FAILURE(1);

    final int index;

    private PigeonTransactionResult(final int index) {
      this.index = index;
    }
  }

  public enum PigeonTransactionType {
    GET(0),
    UPDATE(1),
    SET(2),
    DELETE_TYPE(3);

    final int index;

    private PigeonTransactionType(final int index) {
      this.index = index;
    }
  }

  public enum AggregateType {
    COUNT(0),
    SUM(1),
    AVERAGE(2);

    final int index;

    private AggregateType(final int index) {
      this.index = index;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class PigeonFirebaseSettings {
    private @Nullable Boolean persistenceEnabled;

    public @Nullable Boolean getPersistenceEnabled() {
      return persistenceEnabled;
    }

    public void setPersistenceEnabled(@Nullable Boolean setterArg) {
      this.persistenceEnabled = setterArg;
    }

    private @Nullable String host;

    public @Nullable String getHost() {
      return host;
    }

    public void setHost(@Nullable String setterArg) {
      this.host = setterArg;
    }

    private @Nullable Boolean sslEnabled;

    public @Nullable Boolean getSslEnabled() {
      return sslEnabled;
    }

    public void setSslEnabled(@Nullable Boolean setterArg) {
      this.sslEnabled = setterArg;
    }

    private @Nullable Long cacheSizeBytes;

    public @Nullable Long getCacheSizeBytes() {
      return cacheSizeBytes;
    }

    public void setCacheSizeBytes(@Nullable Long setterArg) {
      this.cacheSizeBytes = setterArg;
    }

    private @NonNull Boolean ignoreUndefinedProperties;

    public @NonNull Boolean getIgnoreUndefinedProperties() {
      return ignoreUndefinedProperties;
    }

    public void setIgnoreUndefinedProperties(@NonNull Boolean setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"ignoreUndefinedProperties\" is null.");
      }
      this.ignoreUndefinedProperties = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    PigeonFirebaseSettings() {}

    public static final class Builder {

      private @Nullable Boolean persistenceEnabled;

      public @NonNull Builder setPersistenceEnabled(@Nullable Boolean setterArg) {
        this.persistenceEnabled = setterArg;
        return this;
      }

      private @Nullable String host;

      public @NonNull Builder setHost(@Nullable String setterArg) {
        this.host = setterArg;
        return this;
      }

      private @Nullable Boolean sslEnabled;

      public @NonNull Builder setSslEnabled(@Nullable Boolean setterArg) {
        this.sslEnabled = setterArg;
        return this;
      }

      private @Nullable Long cacheSizeBytes;

      public @NonNull Builder setCacheSizeBytes(@Nullable Long setterArg) {
        this.cacheSizeBytes = setterArg;
        return this;
      }

      private @Nullable Boolean ignoreUndefinedProperties;

      public @NonNull Builder setIgnoreUndefinedProperties(@NonNull Boolean setterArg) {
        this.ignoreUndefinedProperties = setterArg;
        return this;
      }

      public @NonNull PigeonFirebaseSettings build() {
        PigeonFirebaseSettings pigeonReturn = new PigeonFirebaseSettings();
        pigeonReturn.setPersistenceEnabled(persistenceEnabled);
        pigeonReturn.setHost(host);
        pigeonReturn.setSslEnabled(sslEnabled);
        pigeonReturn.setCacheSizeBytes(cacheSizeBytes);
        pigeonReturn.setIgnoreUndefinedProperties(ignoreUndefinedProperties);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(5);
      toListResult.add(persistenceEnabled);
      toListResult.add(host);
      toListResult.add(sslEnabled);
      toListResult.add(cacheSizeBytes);
      toListResult.add(ignoreUndefinedProperties);
      return toListResult;
    }

    static @NonNull PigeonFirebaseSettings fromList(@NonNull ArrayList<Object> list) {
      PigeonFirebaseSettings pigeonResult = new PigeonFirebaseSettings();
      Object persistenceEnabled = list.get(0);
      pigeonResult.setPersistenceEnabled((Boolean) persistenceEnabled);
      Object host = list.get(1);
      pigeonResult.setHost((String) host);
      Object sslEnabled = list.get(2);
      pigeonResult.setSslEnabled((Boolean) sslEnabled);
      Object cacheSizeBytes = list.get(3);
      pigeonResult.setCacheSizeBytes(
          (cacheSizeBytes == null)
              ? null
              : ((cacheSizeBytes instanceof Integer)
                  ? (Integer) cacheSizeBytes
                  : (Long) cacheSizeBytes));
      Object ignoreUndefinedProperties = list.get(4);
      pigeonResult.setIgnoreUndefinedProperties((Boolean) ignoreUndefinedProperties);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class FirestorePigeonFirebaseApp {
    private @NonNull String appName;

    public @NonNull String getAppName() {
      return appName;
    }

    public void setAppName(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"appName\" is null.");
      }
      this.appName = setterArg;
    }

    private @NonNull PigeonFirebaseSettings settings;

    public @NonNull PigeonFirebaseSettings getSettings() {
      return settings;
    }

    public void setSettings(@NonNull PigeonFirebaseSettings setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"settings\" is null.");
      }
      this.settings = setterArg;
    }

    private @NonNull String databaseURL;

    public @NonNull String getDatabaseURL() {
      return databaseURL;
    }

    public void setDatabaseURL(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"databaseURL\" is null.");
      }
      this.databaseURL = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    FirestorePigeonFirebaseApp() {}

    public static final class Builder {

      private @Nullable String appName;

      public @NonNull Builder setAppName(@NonNull String setterArg) {
        this.appName = setterArg;
        return this;
      }

      private @Nullable PigeonFirebaseSettings settings;

      public @NonNull Builder setSettings(@NonNull PigeonFirebaseSettings setterArg) {
        this.settings = setterArg;
        return this;
      }

      private @Nullable String databaseURL;

      public @NonNull Builder setDatabaseURL(@NonNull String setterArg) {
        this.databaseURL = setterArg;
        return this;
      }

      public @NonNull FirestorePigeonFirebaseApp build() {
        FirestorePigeonFirebaseApp pigeonReturn = new FirestorePigeonFirebaseApp();
        pigeonReturn.setAppName(appName);
        pigeonReturn.setSettings(settings);
        pigeonReturn.setDatabaseURL(databaseURL);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(3);
      toListResult.add(appName);
      toListResult.add((settings == null) ? null : settings.toList());
      toListResult.add(databaseURL);
      return toListResult;
    }

    static @NonNull FirestorePigeonFirebaseApp fromList(@NonNull ArrayList<Object> list) {
      FirestorePigeonFirebaseApp pigeonResult = new FirestorePigeonFirebaseApp();
      Object appName = list.get(0);
      pigeonResult.setAppName((String) appName);
      Object settings = list.get(1);
      pigeonResult.setSettings(
          (settings == null)
              ? null
              : PigeonFirebaseSettings.fromList((ArrayList<Object>) settings));
      Object databaseURL = list.get(2);
      pigeonResult.setDatabaseURL((String) databaseURL);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class PigeonSnapshotMetadata {
    private @NonNull Boolean hasPendingWrites;

    public @NonNull Boolean getHasPendingWrites() {
      return hasPendingWrites;
    }

    public void setHasPendingWrites(@NonNull Boolean setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"hasPendingWrites\" is null.");
      }
      this.hasPendingWrites = setterArg;
    }

    private @NonNull Boolean isFromCache;

    public @NonNull Boolean getIsFromCache() {
      return isFromCache;
    }

    public void setIsFromCache(@NonNull Boolean setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"isFromCache\" is null.");
      }
      this.isFromCache = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    PigeonSnapshotMetadata() {}

    public static final class Builder {

      private @Nullable Boolean hasPendingWrites;

      public @NonNull Builder setHasPendingWrites(@NonNull Boolean setterArg) {
        this.hasPendingWrites = setterArg;
        return this;
      }

      private @Nullable Boolean isFromCache;

      public @NonNull Builder setIsFromCache(@NonNull Boolean setterArg) {
        this.isFromCache = setterArg;
        return this;
      }

      public @NonNull PigeonSnapshotMetadata build() {
        PigeonSnapshotMetadata pigeonReturn = new PigeonSnapshotMetadata();
        pigeonReturn.setHasPendingWrites(hasPendingWrites);
        pigeonReturn.setIsFromCache(isFromCache);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(2);
      toListResult.add(hasPendingWrites);
      toListResult.add(isFromCache);
      return toListResult;
    }

    static @NonNull PigeonSnapshotMetadata fromList(@NonNull ArrayList<Object> list) {
      PigeonSnapshotMetadata pigeonResult = new PigeonSnapshotMetadata();
      Object hasPendingWrites = list.get(0);
      pigeonResult.setHasPendingWrites((Boolean) hasPendingWrites);
      Object isFromCache = list.get(1);
      pigeonResult.setIsFromCache((Boolean) isFromCache);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class PigeonDocumentSnapshot {
    private @NonNull String path;

    public @NonNull String getPath() {
      return path;
    }

    public void setPath(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"path\" is null.");
      }
      this.path = setterArg;
    }

    private @Nullable Map<String, Object> data;

    public @Nullable Map<String, Object> getData() {
      return data;
    }

    public void setData(@Nullable Map<String, Object> setterArg) {
      this.data = setterArg;
    }

    private @NonNull PigeonSnapshotMetadata metadata;

    public @NonNull PigeonSnapshotMetadata getMetadata() {
      return metadata;
    }

    public void setMetadata(@NonNull PigeonSnapshotMetadata setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"metadata\" is null.");
      }
      this.metadata = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    PigeonDocumentSnapshot() {}

    public static final class Builder {

      private @Nullable String path;

      public @NonNull Builder setPath(@NonNull String setterArg) {
        this.path = setterArg;
        return this;
      }

      private @Nullable Map<String, Object> data;

      public @NonNull Builder setData(@Nullable Map<String, Object> setterArg) {
        this.data = setterArg;
        return this;
      }

      private @Nullable PigeonSnapshotMetadata metadata;

      public @NonNull Builder setMetadata(@NonNull PigeonSnapshotMetadata setterArg) {
        this.metadata = setterArg;
        return this;
      }

      public @NonNull PigeonDocumentSnapshot build() {
        PigeonDocumentSnapshot pigeonReturn = new PigeonDocumentSnapshot();
        pigeonReturn.setPath(path);
        pigeonReturn.setData(data);
        pigeonReturn.setMetadata(metadata);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(3);
      toListResult.add(path);
      toListResult.add(data);
      toListResult.add((metadata == null) ? null : metadata.toList());
      return toListResult;
    }

    static @NonNull PigeonDocumentSnapshot fromList(@NonNull ArrayList<Object> list) {
      PigeonDocumentSnapshot pigeonResult = new PigeonDocumentSnapshot();
      Object path = list.get(0);
      pigeonResult.setPath((String) path);
      Object data = list.get(1);
      pigeonResult.setData((Map<String, Object>) data);
      Object metadata = list.get(2);
      pigeonResult.setMetadata(
          (metadata == null)
              ? null
              : PigeonSnapshotMetadata.fromList((ArrayList<Object>) metadata));
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class PigeonDocumentChange {
    private @NonNull DocumentChangeType type;

    public @NonNull DocumentChangeType getType() {
      return type;
    }

    public void setType(@NonNull DocumentChangeType setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"type\" is null.");
      }
      this.type = setterArg;
    }

    private @NonNull PigeonDocumentSnapshot document;

    public @NonNull PigeonDocumentSnapshot getDocument() {
      return document;
    }

    public void setDocument(@NonNull PigeonDocumentSnapshot setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"document\" is null.");
      }
      this.document = setterArg;
    }

    private @NonNull Long oldIndex;

    public @NonNull Long getOldIndex() {
      return oldIndex;
    }

    public void setOldIndex(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"oldIndex\" is null.");
      }
      this.oldIndex = setterArg;
    }

    private @NonNull Long newIndex;

    public @NonNull Long getNewIndex() {
      return newIndex;
    }

    public void setNewIndex(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"newIndex\" is null.");
      }
      this.newIndex = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    PigeonDocumentChange() {}

    public static final class Builder {

      private @Nullable DocumentChangeType type;

      public @NonNull Builder setType(@NonNull DocumentChangeType setterArg) {
        this.type = setterArg;
        return this;
      }

      private @Nullable PigeonDocumentSnapshot document;

      public @NonNull Builder setDocument(@NonNull PigeonDocumentSnapshot setterArg) {
        this.document = setterArg;
        return this;
      }

      private @Nullable Long oldIndex;

      public @NonNull Builder setOldIndex(@NonNull Long setterArg) {
        this.oldIndex = setterArg;
        return this;
      }

      private @Nullable Long newIndex;

      public @NonNull Builder setNewIndex(@NonNull Long setterArg) {
        this.newIndex = setterArg;
        return this;
      }

      public @NonNull PigeonDocumentChange build() {
        PigeonDocumentChange pigeonReturn = new PigeonDocumentChange();
        pigeonReturn.setType(type);
        pigeonReturn.setDocument(document);
        pigeonReturn.setOldIndex(oldIndex);
        pigeonReturn.setNewIndex(newIndex);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(4);
      toListResult.add(type == null ? null : type.index);
      toListResult.add((document == null) ? null : document.toList());
      toListResult.add(oldIndex);
      toListResult.add(newIndex);
      return toListResult;
    }

    static @NonNull PigeonDocumentChange fromList(@NonNull ArrayList<Object> list) {
      PigeonDocumentChange pigeonResult = new PigeonDocumentChange();
      Object type = list.get(0);
      pigeonResult.setType(DocumentChangeType.values()[(int) type]);
      Object document = list.get(1);
      pigeonResult.setDocument(
          (document == null)
              ? null
              : PigeonDocumentSnapshot.fromList((ArrayList<Object>) document));
      Object oldIndex = list.get(2);
      pigeonResult.setOldIndex(
          (oldIndex == null)
              ? null
              : ((oldIndex instanceof Integer) ? (Integer) oldIndex : (Long) oldIndex));
      Object newIndex = list.get(3);
      pigeonResult.setNewIndex(
          (newIndex == null)
              ? null
              : ((newIndex instanceof Integer) ? (Integer) newIndex : (Long) newIndex));
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class PigeonQuerySnapshot {
    private @NonNull List<PigeonDocumentSnapshot> documents;

    public @NonNull List<PigeonDocumentSnapshot> getDocuments() {
      return documents;
    }

    public void setDocuments(@NonNull List<PigeonDocumentSnapshot> setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"documents\" is null.");
      }
      this.documents = setterArg;
    }

    private @NonNull List<PigeonDocumentChange> documentChanges;

    public @NonNull List<PigeonDocumentChange> getDocumentChanges() {
      return documentChanges;
    }

    public void setDocumentChanges(@NonNull List<PigeonDocumentChange> setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"documentChanges\" is null.");
      }
      this.documentChanges = setterArg;
    }

    private @NonNull PigeonSnapshotMetadata metadata;

    public @NonNull PigeonSnapshotMetadata getMetadata() {
      return metadata;
    }

    public void setMetadata(@NonNull PigeonSnapshotMetadata setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"metadata\" is null.");
      }
      this.metadata = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    PigeonQuerySnapshot() {}

    public static final class Builder {

      private @Nullable List<PigeonDocumentSnapshot> documents;

      public @NonNull Builder setDocuments(@NonNull List<PigeonDocumentSnapshot> setterArg) {
        this.documents = setterArg;
        return this;
      }

      private @Nullable List<PigeonDocumentChange> documentChanges;

      public @NonNull Builder setDocumentChanges(@NonNull List<PigeonDocumentChange> setterArg) {
        this.documentChanges = setterArg;
        return this;
      }

      private @Nullable PigeonSnapshotMetadata metadata;

      public @NonNull Builder setMetadata(@NonNull PigeonSnapshotMetadata setterArg) {
        this.metadata = setterArg;
        return this;
      }

      public @NonNull PigeonQuerySnapshot build() {
        PigeonQuerySnapshot pigeonReturn = new PigeonQuerySnapshot();
        pigeonReturn.setDocuments(documents);
        pigeonReturn.setDocumentChanges(documentChanges);
        pigeonReturn.setMetadata(metadata);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(3);
      toListResult.add(documents);
      toListResult.add(documentChanges);
      toListResult.add((metadata == null) ? null : metadata.toList());
      return toListResult;
    }

    static @NonNull PigeonQuerySnapshot fromList(@NonNull ArrayList<Object> list) {
      PigeonQuerySnapshot pigeonResult = new PigeonQuerySnapshot();
      Object documents = list.get(0);
      pigeonResult.setDocuments((List<PigeonDocumentSnapshot>) documents);
      Object documentChanges = list.get(1);
      pigeonResult.setDocumentChanges((List<PigeonDocumentChange>) documentChanges);
      Object metadata = list.get(2);
      pigeonResult.setMetadata(
          (metadata == null)
              ? null
              : PigeonSnapshotMetadata.fromList((ArrayList<Object>) metadata));
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class PigeonGetOptions {
    private @NonNull Source source;

    public @NonNull Source getSource() {
      return source;
    }

    public void setSource(@NonNull Source setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"source\" is null.");
      }
      this.source = setterArg;
    }

    private @NonNull ServerTimestampBehavior serverTimestampBehavior;

    public @NonNull ServerTimestampBehavior getServerTimestampBehavior() {
      return serverTimestampBehavior;
    }

    public void setServerTimestampBehavior(@NonNull ServerTimestampBehavior setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"serverTimestampBehavior\" is null.");
      }
      this.serverTimestampBehavior = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    PigeonGetOptions() {}

    public static final class Builder {

      private @Nullable Source source;

      public @NonNull Builder setSource(@NonNull Source setterArg) {
        this.source = setterArg;
        return this;
      }

      private @Nullable ServerTimestampBehavior serverTimestampBehavior;

      public @NonNull Builder setServerTimestampBehavior(
          @NonNull ServerTimestampBehavior setterArg) {
        this.serverTimestampBehavior = setterArg;
        return this;
      }

      public @NonNull PigeonGetOptions build() {
        PigeonGetOptions pigeonReturn = new PigeonGetOptions();
        pigeonReturn.setSource(source);
        pigeonReturn.setServerTimestampBehavior(serverTimestampBehavior);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(2);
      toListResult.add(source == null ? null : source.index);
      toListResult.add(serverTimestampBehavior == null ? null : serverTimestampBehavior.index);
      return toListResult;
    }

    static @NonNull PigeonGetOptions fromList(@NonNull ArrayList<Object> list) {
      PigeonGetOptions pigeonResult = new PigeonGetOptions();
      Object source = list.get(0);
      pigeonResult.setSource(Source.values()[(int) source]);
      Object serverTimestampBehavior = list.get(1);
      pigeonResult.setServerTimestampBehavior(
          ServerTimestampBehavior.values()[(int) serverTimestampBehavior]);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class PigeonDocumentOption {
    private @Nullable Boolean merge;

    public @Nullable Boolean getMerge() {
      return merge;
    }

    public void setMerge(@Nullable Boolean setterArg) {
      this.merge = setterArg;
    }

    private @Nullable List<List<String>> mergeFields;

    public @Nullable List<List<String>> getMergeFields() {
      return mergeFields;
    }

    public void setMergeFields(@Nullable List<List<String>> setterArg) {
      this.mergeFields = setterArg;
    }

    public static final class Builder {

      private @Nullable Boolean merge;

      public @NonNull Builder setMerge(@Nullable Boolean setterArg) {
        this.merge = setterArg;
        return this;
      }

      private @Nullable List<List<String>> mergeFields;

      public @NonNull Builder setMergeFields(@Nullable List<List<String>> setterArg) {
        this.mergeFields = setterArg;
        return this;
      }

      public @NonNull PigeonDocumentOption build() {
        PigeonDocumentOption pigeonReturn = new PigeonDocumentOption();
        pigeonReturn.setMerge(merge);
        pigeonReturn.setMergeFields(mergeFields);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(2);
      toListResult.add(merge);
      toListResult.add(mergeFields);
      return toListResult;
    }

    static @NonNull PigeonDocumentOption fromList(@NonNull ArrayList<Object> list) {
      PigeonDocumentOption pigeonResult = new PigeonDocumentOption();
      Object merge = list.get(0);
      pigeonResult.setMerge((Boolean) merge);
      Object mergeFields = list.get(1);
      pigeonResult.setMergeFields((List<List<String>>) mergeFields);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class PigeonTransactionCommand {
    private @NonNull PigeonTransactionType type;

    public @NonNull PigeonTransactionType getType() {
      return type;
    }

    public void setType(@NonNull PigeonTransactionType setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"type\" is null.");
      }
      this.type = setterArg;
    }

    private @NonNull String path;

    public @NonNull String getPath() {
      return path;
    }

    public void setPath(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"path\" is null.");
      }
      this.path = setterArg;
    }

    private @Nullable Map<String, Object> data;

    public @Nullable Map<String, Object> getData() {
      return data;
    }

    public void setData(@Nullable Map<String, Object> setterArg) {
      this.data = setterArg;
    }

    private @Nullable PigeonDocumentOption option;

    public @Nullable PigeonDocumentOption getOption() {
      return option;
    }

    public void setOption(@Nullable PigeonDocumentOption setterArg) {
      this.option = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    PigeonTransactionCommand() {}

    public static final class Builder {

      private @Nullable PigeonTransactionType type;

      public @NonNull Builder setType(@NonNull PigeonTransactionType setterArg) {
        this.type = setterArg;
        return this;
      }

      private @Nullable String path;

      public @NonNull Builder setPath(@NonNull String setterArg) {
        this.path = setterArg;
        return this;
      }

      private @Nullable Map<String, Object> data;

      public @NonNull Builder setData(@Nullable Map<String, Object> setterArg) {
        this.data = setterArg;
        return this;
      }

      private @Nullable PigeonDocumentOption option;

      public @NonNull Builder setOption(@Nullable PigeonDocumentOption setterArg) {
        this.option = setterArg;
        return this;
      }

      public @NonNull PigeonTransactionCommand build() {
        PigeonTransactionCommand pigeonReturn = new PigeonTransactionCommand();
        pigeonReturn.setType(type);
        pigeonReturn.setPath(path);
        pigeonReturn.setData(data);
        pigeonReturn.setOption(option);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(4);
      toListResult.add(type == null ? null : type.index);
      toListResult.add(path);
      toListResult.add(data);
      toListResult.add((option == null) ? null : option.toList());
      return toListResult;
    }

    static @NonNull PigeonTransactionCommand fromList(@NonNull ArrayList<Object> list) {
      PigeonTransactionCommand pigeonResult = new PigeonTransactionCommand();
      Object type = list.get(0);
      pigeonResult.setType(PigeonTransactionType.values()[(int) type]);
      Object path = list.get(1);
      pigeonResult.setPath((String) path);
      Object data = list.get(2);
      pigeonResult.setData((Map<String, Object>) data);
      Object option = list.get(3);
      pigeonResult.setOption(
          (option == null) ? null : PigeonDocumentOption.fromList((ArrayList<Object>) option));
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class DocumentReferenceRequest {
    private @NonNull String path;

    public @NonNull String getPath() {
      return path;
    }

    public void setPath(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"path\" is null.");
      }
      this.path = setterArg;
    }

    private @Nullable Map<Object, Object> data;

    public @Nullable Map<Object, Object> getData() {
      return data;
    }

    public void setData(@Nullable Map<Object, Object> setterArg) {
      this.data = setterArg;
    }

    private @Nullable PigeonDocumentOption option;

    public @Nullable PigeonDocumentOption getOption() {
      return option;
    }

    public void setOption(@Nullable PigeonDocumentOption setterArg) {
      this.option = setterArg;
    }

    private @Nullable Source source;

    public @Nullable Source getSource() {
      return source;
    }

    public void setSource(@Nullable Source setterArg) {
      this.source = setterArg;
    }

    private @Nullable ServerTimestampBehavior serverTimestampBehavior;

    public @Nullable ServerTimestampBehavior getServerTimestampBehavior() {
      return serverTimestampBehavior;
    }

    public void setServerTimestampBehavior(@Nullable ServerTimestampBehavior setterArg) {
      this.serverTimestampBehavior = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    DocumentReferenceRequest() {}

    public static final class Builder {

      private @Nullable String path;

      public @NonNull Builder setPath(@NonNull String setterArg) {
        this.path = setterArg;
        return this;
      }

      private @Nullable Map<Object, Object> data;

      public @NonNull Builder setData(@Nullable Map<Object, Object> setterArg) {
        this.data = setterArg;
        return this;
      }

      private @Nullable PigeonDocumentOption option;

      public @NonNull Builder setOption(@Nullable PigeonDocumentOption setterArg) {
        this.option = setterArg;
        return this;
      }

      private @Nullable Source source;

      public @NonNull Builder setSource(@Nullable Source setterArg) {
        this.source = setterArg;
        return this;
      }

      private @Nullable ServerTimestampBehavior serverTimestampBehavior;

      public @NonNull Builder setServerTimestampBehavior(
          @Nullable ServerTimestampBehavior setterArg) {
        this.serverTimestampBehavior = setterArg;
        return this;
      }

      public @NonNull DocumentReferenceRequest build() {
        DocumentReferenceRequest pigeonReturn = new DocumentReferenceRequest();
        pigeonReturn.setPath(path);
        pigeonReturn.setData(data);
        pigeonReturn.setOption(option);
        pigeonReturn.setSource(source);
        pigeonReturn.setServerTimestampBehavior(serverTimestampBehavior);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(5);
      toListResult.add(path);
      toListResult.add(data);
      toListResult.add((option == null) ? null : option.toList());
      toListResult.add(source == null ? null : source.index);
      toListResult.add(serverTimestampBehavior == null ? null : serverTimestampBehavior.index);
      return toListResult;
    }

    static @NonNull DocumentReferenceRequest fromList(@NonNull ArrayList<Object> list) {
      DocumentReferenceRequest pigeonResult = new DocumentReferenceRequest();
      Object path = list.get(0);
      pigeonResult.setPath((String) path);
      Object data = list.get(1);
      pigeonResult.setData((Map<Object, Object>) data);
      Object option = list.get(2);
      pigeonResult.setOption(
          (option == null) ? null : PigeonDocumentOption.fromList((ArrayList<Object>) option));
      Object source = list.get(3);
      pigeonResult.setSource(source == null ? null : Source.values()[(int) source]);
      Object serverTimestampBehavior = list.get(4);
      pigeonResult.setServerTimestampBehavior(
          serverTimestampBehavior == null
              ? null
              : ServerTimestampBehavior.values()[(int) serverTimestampBehavior]);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class PigeonQueryParameters {
    private @Nullable List<List<Object>> where;

    public @Nullable List<List<Object>> getWhere() {
      return where;
    }

    public void setWhere(@Nullable List<List<Object>> setterArg) {
      this.where = setterArg;
    }

    private @Nullable List<List<Object>> orderBy;

    public @Nullable List<List<Object>> getOrderBy() {
      return orderBy;
    }

    public void setOrderBy(@Nullable List<List<Object>> setterArg) {
      this.orderBy = setterArg;
    }

    private @Nullable Long limit;

    public @Nullable Long getLimit() {
      return limit;
    }

    public void setLimit(@Nullable Long setterArg) {
      this.limit = setterArg;
    }

    private @Nullable Long limitToLast;

    public @Nullable Long getLimitToLast() {
      return limitToLast;
    }

    public void setLimitToLast(@Nullable Long setterArg) {
      this.limitToLast = setterArg;
    }

    private @Nullable List<Object> startAt;

    public @Nullable List<Object> getStartAt() {
      return startAt;
    }

    public void setStartAt(@Nullable List<Object> setterArg) {
      this.startAt = setterArg;
    }

    private @Nullable List<Object> startAfter;

    public @Nullable List<Object> getStartAfter() {
      return startAfter;
    }

    public void setStartAfter(@Nullable List<Object> setterArg) {
      this.startAfter = setterArg;
    }

    private @Nullable List<Object> endAt;

    public @Nullable List<Object> getEndAt() {
      return endAt;
    }

    public void setEndAt(@Nullable List<Object> setterArg) {
      this.endAt = setterArg;
    }

    private @Nullable List<Object> endBefore;

    public @Nullable List<Object> getEndBefore() {
      return endBefore;
    }

    public void setEndBefore(@Nullable List<Object> setterArg) {
      this.endBefore = setterArg;
    }

    private @Nullable Map<String, Object> filters;

    public @Nullable Map<String, Object> getFilters() {
      return filters;
    }

    public void setFilters(@Nullable Map<String, Object> setterArg) {
      this.filters = setterArg;
    }

    public static final class Builder {

      private @Nullable List<List<Object>> where;

      public @NonNull Builder setWhere(@Nullable List<List<Object>> setterArg) {
        this.where = setterArg;
        return this;
      }

      private @Nullable List<List<Object>> orderBy;

      public @NonNull Builder setOrderBy(@Nullable List<List<Object>> setterArg) {
        this.orderBy = setterArg;
        return this;
      }

      private @Nullable Long limit;

      public @NonNull Builder setLimit(@Nullable Long setterArg) {
        this.limit = setterArg;
        return this;
      }

      private @Nullable Long limitToLast;

      public @NonNull Builder setLimitToLast(@Nullable Long setterArg) {
        this.limitToLast = setterArg;
        return this;
      }

      private @Nullable List<Object> startAt;

      public @NonNull Builder setStartAt(@Nullable List<Object> setterArg) {
        this.startAt = setterArg;
        return this;
      }

      private @Nullable List<Object> startAfter;

      public @NonNull Builder setStartAfter(@Nullable List<Object> setterArg) {
        this.startAfter = setterArg;
        return this;
      }

      private @Nullable List<Object> endAt;

      public @NonNull Builder setEndAt(@Nullable List<Object> setterArg) {
        this.endAt = setterArg;
        return this;
      }

      private @Nullable List<Object> endBefore;

      public @NonNull Builder setEndBefore(@Nullable List<Object> setterArg) {
        this.endBefore = setterArg;
        return this;
      }

      private @Nullable Map<String, Object> filters;

      public @NonNull Builder setFilters(@Nullable Map<String, Object> setterArg) {
        this.filters = setterArg;
        return this;
      }

      public @NonNull PigeonQueryParameters build() {
        PigeonQueryParameters pigeonReturn = new PigeonQueryParameters();
        pigeonReturn.setWhere(where);
        pigeonReturn.setOrderBy(orderBy);
        pigeonReturn.setLimit(limit);
        pigeonReturn.setLimitToLast(limitToLast);
        pigeonReturn.setStartAt(startAt);
        pigeonReturn.setStartAfter(startAfter);
        pigeonReturn.setEndAt(endAt);
        pigeonReturn.setEndBefore(endBefore);
        pigeonReturn.setFilters(filters);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(9);
      toListResult.add(where);
      toListResult.add(orderBy);
      toListResult.add(limit);
      toListResult.add(limitToLast);
      toListResult.add(startAt);
      toListResult.add(startAfter);
      toListResult.add(endAt);
      toListResult.add(endBefore);
      toListResult.add(filters);
      return toListResult;
    }

    static @NonNull PigeonQueryParameters fromList(@NonNull ArrayList<Object> list) {
      PigeonQueryParameters pigeonResult = new PigeonQueryParameters();
      Object where = list.get(0);
      pigeonResult.setWhere((List<List<Object>>) where);
      Object orderBy = list.get(1);
      pigeonResult.setOrderBy((List<List<Object>>) orderBy);
      Object limit = list.get(2);
      pigeonResult.setLimit(
          (limit == null) ? null : ((limit instanceof Integer) ? (Integer) limit : (Long) limit));
      Object limitToLast = list.get(3);
      pigeonResult.setLimitToLast(
          (limitToLast == null)
              ? null
              : ((limitToLast instanceof Integer) ? (Integer) limitToLast : (Long) limitToLast));
      Object startAt = list.get(4);
      pigeonResult.setStartAt((List<Object>) startAt);
      Object startAfter = list.get(5);
      pigeonResult.setStartAfter((List<Object>) startAfter);
      Object endAt = list.get(6);
      pigeonResult.setEndAt((List<Object>) endAt);
      Object endBefore = list.get(7);
      pigeonResult.setEndBefore((List<Object>) endBefore);
      Object filters = list.get(8);
      pigeonResult.setFilters((Map<String, Object>) filters);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class AggregateQuery {
    private @NonNull AggregateType type;

    public @NonNull AggregateType getType() {
      return type;
    }

    public void setType(@NonNull AggregateType setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"type\" is null.");
      }
      this.type = setterArg;
    }

    private @Nullable String field;

    public @Nullable String getField() {
      return field;
    }

    public void setField(@Nullable String setterArg) {
      this.field = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    AggregateQuery() {}

    public static final class Builder {

      private @Nullable AggregateType type;

      public @NonNull Builder setType(@NonNull AggregateType setterArg) {
        this.type = setterArg;
        return this;
      }

      private @Nullable String field;

      public @NonNull Builder setField(@Nullable String setterArg) {
        this.field = setterArg;
        return this;
      }

      public @NonNull AggregateQuery build() {
        AggregateQuery pigeonReturn = new AggregateQuery();
        pigeonReturn.setType(type);
        pigeonReturn.setField(field);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(2);
      toListResult.add(type == null ? null : type.index);
      toListResult.add(field);
      return toListResult;
    }

    static @NonNull AggregateQuery fromList(@NonNull ArrayList<Object> list) {
      AggregateQuery pigeonResult = new AggregateQuery();
      Object type = list.get(0);
      pigeonResult.setType(AggregateType.values()[(int) type]);
      Object field = list.get(1);
      pigeonResult.setField((String) field);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class AggregateQueryResponse {
    private @NonNull AggregateType type;

    public @NonNull AggregateType getType() {
      return type;
    }

    public void setType(@NonNull AggregateType setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"type\" is null.");
      }
      this.type = setterArg;
    }

    private @Nullable String field;

    public @Nullable String getField() {
      return field;
    }

    public void setField(@Nullable String setterArg) {
      this.field = setterArg;
    }

    private @Nullable Double value;

    public @Nullable Double getValue() {
      return value;
    }

    public void setValue(@Nullable Double setterArg) {
      this.value = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    AggregateQueryResponse() {}

    public static final class Builder {

      private @Nullable AggregateType type;

      public @NonNull Builder setType(@NonNull AggregateType setterArg) {
        this.type = setterArg;
        return this;
      }

      private @Nullable String field;

      public @NonNull Builder setField(@Nullable String setterArg) {
        this.field = setterArg;
        return this;
      }

      private @Nullable Double value;

      public @NonNull Builder setValue(@Nullable Double setterArg) {
        this.value = setterArg;
        return this;
      }

      public @NonNull AggregateQueryResponse build() {
        AggregateQueryResponse pigeonReturn = new AggregateQueryResponse();
        pigeonReturn.setType(type);
        pigeonReturn.setField(field);
        pigeonReturn.setValue(value);
        return pigeonReturn;
      }
    }

    @NonNull
    public ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(3);
      toListResult.add(type == null ? null : type.index);
      toListResult.add(field);
      toListResult.add(value);
      return toListResult;
    }

    static @NonNull AggregateQueryResponse fromList(@NonNull ArrayList<Object> list) {
      AggregateQueryResponse pigeonResult = new AggregateQueryResponse();
      Object type = list.get(0);
      pigeonResult.setType(AggregateType.values()[(int) type]);
      Object field = list.get(1);
      pigeonResult.setField((String) field);
      Object value = list.get(2);
      pigeonResult.setValue((Double) value);
      return pigeonResult;
    }
  }

  public interface Result<T> {
    @SuppressWarnings("UnknownNullness")
    void success(T result);

    void error(@NonNull Throwable error);
  }

  private static class FirebaseFirestoreHostApiCodec extends FlutterFirebaseFirestoreMessageCodec {
    public static final FirebaseFirestoreHostApiCodec INSTANCE =
        new FirebaseFirestoreHostApiCodec();

    private FirebaseFirestoreHostApiCodec() {}

    @Override
    protected Object readValueOfType(byte type, @NonNull ByteBuffer buffer) {
      switch (type) {
        case (byte) 128:
          return AggregateQuery.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 129:
          return AggregateQueryResponse.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 130:
          return DocumentReferenceRequest.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 131:
          return FirestorePigeonFirebaseApp.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 132:
          return PigeonDocumentChange.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 133:
          return PigeonDocumentOption.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 134:
          return PigeonDocumentSnapshot.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 135:
          return PigeonFirebaseSettings.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 136:
          return PigeonGetOptions.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 137:
          return PigeonQueryParameters.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 138:
          return PigeonQuerySnapshot.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 139:
          return PigeonSnapshotMetadata.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 140:
          return PigeonTransactionCommand.fromList((ArrayList<Object>) readValue(buffer));
        default:
          return super.readValueOfType(type, buffer);
      }
    }

    @Override
    protected void writeValue(@NonNull ByteArrayOutputStream stream, Object value) {
      if (value instanceof AggregateQuery) {
        stream.write(128);
        writeValue(stream, ((AggregateQuery) value).toList());
      } else if (value instanceof AggregateQueryResponse) {
        stream.write(129);
        writeValue(stream, ((AggregateQueryResponse) value).toList());
      } else if (value instanceof DocumentReferenceRequest) {
        stream.write(130);
        writeValue(stream, ((DocumentReferenceRequest) value).toList());
      } else if (value instanceof FirestorePigeonFirebaseApp) {
        stream.write(131);
        writeValue(stream, ((FirestorePigeonFirebaseApp) value).toList());
      } else if (value instanceof PigeonDocumentChange) {
        stream.write(132);
        writeValue(stream, ((PigeonDocumentChange) value).toList());
      } else if (value instanceof PigeonDocumentOption) {
        stream.write(133);
        writeValue(stream, ((PigeonDocumentOption) value).toList());
      } else if (value instanceof PigeonDocumentSnapshot) {
        stream.write(134);
        writeValue(stream, ((PigeonDocumentSnapshot) value).toList());
      } else if (value instanceof PigeonFirebaseSettings) {
        stream.write(135);
        writeValue(stream, ((PigeonFirebaseSettings) value).toList());
      } else if (value instanceof PigeonGetOptions) {
        stream.write(136);
        writeValue(stream, ((PigeonGetOptions) value).toList());
      } else if (value instanceof PigeonQueryParameters) {
        stream.write(137);
        writeValue(stream, ((PigeonQueryParameters) value).toList());
      } else if (value instanceof PigeonQuerySnapshot) {
        stream.write(138);
        writeValue(stream, ((PigeonQuerySnapshot) value).toList());
      } else if (value instanceof PigeonSnapshotMetadata) {
        stream.write(139);
        writeValue(stream, ((PigeonSnapshotMetadata) value).toList());
      } else if (value instanceof PigeonTransactionCommand) {
        stream.write(140);
        writeValue(stream, ((PigeonTransactionCommand) value).toList());
      } else {
        super.writeValue(stream, value);
      }
    }
  }

  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface FirebaseFirestoreHostApi {

    void loadBundle(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull byte[] bundle,
        @NonNull Result<String> result);

    void namedQueryGet(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull String name,
        @NonNull PigeonGetOptions options,
        @NonNull Result<PigeonQuerySnapshot> result);

    void clearPersistence(@NonNull FirestorePigeonFirebaseApp app, @NonNull Result<Void> result);

    void disableNetwork(@NonNull FirestorePigeonFirebaseApp app, @NonNull Result<Void> result);

    void enableNetwork(@NonNull FirestorePigeonFirebaseApp app, @NonNull Result<Void> result);

    void terminate(@NonNull FirestorePigeonFirebaseApp app, @NonNull Result<Void> result);

    void waitForPendingWrites(
        @NonNull FirestorePigeonFirebaseApp app, @NonNull Result<Void> result);

    void setIndexConfiguration(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull String indexConfiguration,
        @NonNull Result<Void> result);

    void setLoggingEnabled(@NonNull Boolean loggingEnabled, @NonNull Result<Void> result);

    void snapshotsInSyncSetup(
        @NonNull FirestorePigeonFirebaseApp app, @NonNull Result<String> result);

    void transactionCreate(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull Long timeout,
        @NonNull Long maxAttempts,
        @NonNull Result<String> result);

    void transactionStoreResult(
        @NonNull String transactionId,
        @NonNull PigeonTransactionResult resultType,
        @Nullable List<PigeonTransactionCommand> commands,
        @NonNull Result<Void> result);

    void transactionGet(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull String transactionId,
        @NonNull String path,
        @NonNull Result<PigeonDocumentSnapshot> result);

    void documentReferenceSet(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull DocumentReferenceRequest request,
        @NonNull Result<Void> result);

    void documentReferenceUpdate(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull DocumentReferenceRequest request,
        @NonNull Result<Void> result);

    void documentReferenceGet(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull DocumentReferenceRequest request,
        @NonNull Result<PigeonDocumentSnapshot> result);

    void documentReferenceDelete(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull DocumentReferenceRequest request,
        @NonNull Result<Void> result);

    void queryGet(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull String path,
        @NonNull Boolean isCollectionGroup,
        @NonNull PigeonQueryParameters parameters,
        @NonNull PigeonGetOptions options,
        @NonNull Result<PigeonQuerySnapshot> result);

    void aggregateQuery(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull String path,
        @NonNull PigeonQueryParameters parameters,
        @NonNull AggregateSource source,
        @NonNull List<AggregateQuery> queries,
        @NonNull Boolean isCollectionGroup,
        @NonNull Result<List<AggregateQueryResponse>> result);

    void writeBatchCommit(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull List<PigeonTransactionCommand> writes,
        @NonNull Result<Void> result);

    void querySnapshot(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull String path,
        @NonNull Boolean isCollectionGroup,
        @NonNull PigeonQueryParameters parameters,
        @NonNull PigeonGetOptions options,
        @NonNull Boolean includeMetadataChanges,
        @NonNull ListenSource source,
        @NonNull Result<String> result);

    void documentReferenceSnapshot(
        @NonNull FirestorePigeonFirebaseApp app,
        @NonNull DocumentReferenceRequest parameters,
        @NonNull Boolean includeMetadataChanges,
        @NonNull ListenSource source,
        @NonNull Result<String> result);

    /** The codec used by FirebaseFirestoreHostApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return FirebaseFirestoreHostApiCodec.INSTANCE;
    }
    /**
     * Sets up an instance of `FirebaseFirestoreHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setup(
        @NonNull BinaryMessenger binaryMessenger, @Nullable FirebaseFirestoreHostApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.loadBundle",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                byte[] bundleArg = (byte[]) args.get(1);
                Result<String> resultCallback =
                    new Result<String>() {
                      public void success(String result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.loadBundle(appArg, bundleArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.namedQueryGet",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                String nameArg = (String) args.get(1);
                PigeonGetOptions optionsArg = (PigeonGetOptions) args.get(2);
                Result<PigeonQuerySnapshot> resultCallback =
                    new Result<PigeonQuerySnapshot>() {
                      public void success(PigeonQuerySnapshot result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.namedQueryGet(appArg, nameArg, optionsArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.clearPersistence",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.clearPersistence(appArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.disableNetwork",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.disableNetwork(appArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.enableNetwork",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.enableNetwork(appArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.terminate",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.terminate(appArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.waitForPendingWrites",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.waitForPendingWrites(appArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.setIndexConfiguration",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                String indexConfigurationArg = (String) args.get(1);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.setIndexConfiguration(appArg, indexConfigurationArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.setLoggingEnabled",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Boolean loggingEnabledArg = (Boolean) args.get(0);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.setLoggingEnabled(loggingEnabledArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.snapshotsInSyncSetup",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                Result<String> resultCallback =
                    new Result<String>() {
                      public void success(String result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.snapshotsInSyncSetup(appArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.transactionCreate",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                Number timeoutArg = (Number) args.get(1);
                Number maxAttemptsArg = (Number) args.get(2);
                Result<String> resultCallback =
                    new Result<String>() {
                      public void success(String result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.transactionCreate(
                    appArg,
                    (timeoutArg == null) ? null : timeoutArg.longValue(),
                    (maxAttemptsArg == null) ? null : maxAttemptsArg.longValue(),
                    resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.transactionStoreResult",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String transactionIdArg = (String) args.get(0);
                PigeonTransactionResult resultTypeArg =
                    PigeonTransactionResult.values()[(int) args.get(1)];
                List<PigeonTransactionCommand> commandsArg =
                    (List<PigeonTransactionCommand>) args.get(2);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.transactionStoreResult(
                    transactionIdArg, resultTypeArg, commandsArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.transactionGet",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                String transactionIdArg = (String) args.get(1);
                String pathArg = (String) args.get(2);
                Result<PigeonDocumentSnapshot> resultCallback =
                    new Result<PigeonDocumentSnapshot>() {
                      public void success(PigeonDocumentSnapshot result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.transactionGet(appArg, transactionIdArg, pathArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.documentReferenceSet",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                DocumentReferenceRequest requestArg = (DocumentReferenceRequest) args.get(1);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.documentReferenceSet(appArg, requestArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.documentReferenceUpdate",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                DocumentReferenceRequest requestArg = (DocumentReferenceRequest) args.get(1);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.documentReferenceUpdate(appArg, requestArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.documentReferenceGet",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                DocumentReferenceRequest requestArg = (DocumentReferenceRequest) args.get(1);
                Result<PigeonDocumentSnapshot> resultCallback =
                    new Result<PigeonDocumentSnapshot>() {
                      public void success(PigeonDocumentSnapshot result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.documentReferenceGet(appArg, requestArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.documentReferenceDelete",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                DocumentReferenceRequest requestArg = (DocumentReferenceRequest) args.get(1);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.documentReferenceDelete(appArg, requestArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.queryGet",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                String pathArg = (String) args.get(1);
                Boolean isCollectionGroupArg = (Boolean) args.get(2);
                PigeonQueryParameters parametersArg = (PigeonQueryParameters) args.get(3);
                PigeonGetOptions optionsArg = (PigeonGetOptions) args.get(4);
                Result<PigeonQuerySnapshot> resultCallback =
                    new Result<PigeonQuerySnapshot>() {
                      public void success(PigeonQuerySnapshot result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.queryGet(
                    appArg,
                    pathArg,
                    isCollectionGroupArg,
                    parametersArg,
                    optionsArg,
                    resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.aggregateQuery",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                String pathArg = (String) args.get(1);
                PigeonQueryParameters parametersArg = (PigeonQueryParameters) args.get(2);
                AggregateSource sourceArg = AggregateSource.values()[(int) args.get(3)];
                List<AggregateQuery> queriesArg = (List<AggregateQuery>) args.get(4);
                Boolean isCollectionGroupArg = (Boolean) args.get(5);
                Result<List<AggregateQueryResponse>> resultCallback =
                    new Result<List<AggregateQueryResponse>>() {
                      public void success(List<AggregateQueryResponse> result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.aggregateQuery(
                    appArg,
                    pathArg,
                    parametersArg,
                    sourceArg,
                    queriesArg,
                    isCollectionGroupArg,
                    resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.writeBatchCommit",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                List<PigeonTransactionCommand> writesArg =
                    (List<PigeonTransactionCommand>) args.get(1);
                Result<Void> resultCallback =
                    new Result<Void>() {
                      public void success(Void result) {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.writeBatchCommit(appArg, writesArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.querySnapshot",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                String pathArg = (String) args.get(1);
                Boolean isCollectionGroupArg = (Boolean) args.get(2);
                PigeonQueryParameters parametersArg = (PigeonQueryParameters) args.get(3);
                PigeonGetOptions optionsArg = (PigeonGetOptions) args.get(4);
                Boolean includeMetadataChangesArg = (Boolean) args.get(5);
                ListenSource sourceArg = ListenSource.values()[(int) args.get(6)];
                Result<String> resultCallback =
                    new Result<String>() {
                      public void success(String result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.querySnapshot(
                    appArg,
                    pathArg,
                    isCollectionGroupArg,
                    parametersArg,
                    optionsArg,
                    includeMetadataChangesArg,
                    sourceArg,
                    resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.cloud_firestore_platform_interface.FirebaseFirestoreHostApi.documentReferenceSnapshot",
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                FirestorePigeonFirebaseApp appArg = (FirestorePigeonFirebaseApp) args.get(0);
                DocumentReferenceRequest parametersArg = (DocumentReferenceRequest) args.get(1);
                Boolean includeMetadataChangesArg = (Boolean) args.get(2);
                ListenSource sourceArg = ListenSource.values()[(int) args.get(3)];
                Result<String> resultCallback =
                    new Result<String>() {
                      public void success(String result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.documentReferenceSnapshot(
                    appArg, parametersArg, includeMetadataChangesArg, sourceArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
}
