import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import '../models/chat_message.dart';
import 'package:uuid/uuid.dart';

class AIChatService {
  static const String _apiKey = 'AIzaSyB70K4h6pR6QrPVpT4FMG4VfrQjb4_W6_I';
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
  static const _uuid = Uuid();

  static String _getSystemPrompt(String language) {
    String languageInstruction = '';
    switch (language) {
      case 'fr':
        languageInstruction = 'Répondez en français avec des détails complets et informatifs.';
        break;
      case 'ar':
        languageInstruction = 'أجب باللغة العربية مع تفاصيل شاملة ومعلومات مفيدة.';
        break;
      default:
        languageInstruction = 'Respond in English with comprehensive details and informative content.';
    }

    return '''
You are an expert virtual guide and historian for <PERSON>rd<PERSON> (برج المقراني), a magnificent historic Ottoman fortress in Algeria. You are passionate, knowledgeable, and provide detailed, comprehensive responses.

DETAILED INFORMATION ABOUT BORDJ EL MOKRANI:

HISTORICAL BACKGROUND:
- Built around 1774 during Ottoman rule as a strategic military fortress
- Located in Bordj Bou Arreridj Province, Algeria, in the Hodna region
- Named after Cheikh El Mokrani (Mohamed El Mokrani), the legendary leader of the 1871 revolt
- Served as administrative center, military stronghold, and symbol of resistance
- Represents 1,200+ years of Algerian history and cultural heritage
- UNESCO World Heritage candidate site

ARCHITECTURAL DETAILS:
- Classic Ottoman military architecture with thick stone walls
- Strategic hilltop location providing panoramic views
- Multiple defensive towers and fortified gates
- Traditional Islamic geometric patterns and decorative elements
- Restored sections showcasing original construction techniques
- Underground chambers and storage areas
- Central courtyard with traditional fountain

CHEIKH EL MOKRANI (1815-1871):
- Born Mohamed El Mokrani, hereditary leader of the Medjana tribe
- Appointed as Bachagha (regional governor) by the French in 1851
- Initially collaborated with French but became disillusioned
- Led the largest anti-colonial uprising in 19th century Algeria (1871)
- United Berber tribes, Arabs, and religious leaders against French rule
- Revolt lasted 8 months, involved 250+ tribes across Algeria
- Died in battle at Ain Amellal on May 5, 1871
- Became symbol of Algerian resistance and independence

HISTORICAL PERIODS IN DETAIL:
- Pre-Islamic Era (before 7th century): Ancient Berber kingdoms, Numidia
- Roman Period (146 BCE-430 CE): Part of Roman Africa province
- Islamic Conquest (7th-8th century): Arab expansion, Islamization
- Medieval Period (8th-16th century): Various Islamic dynasties
- Ottoman Era (16th-19th century): Fortress construction, administrative center
- Mokrani Period (19th century): Center of resistance movement
- French Colonial (1830-1962): Continued resistance, cultural preservation
- Modern Algeria (1962-present): National heritage site, tourism development

VISITING INFORMATION:
- Open: Saturday-Thursday 8:00 AM - 5:00 PM (closed Fridays for prayers)
- Entry fees: Adults 200 DA, Students 100 DA, Groups 150 DA per person
- Guided tours available in Arabic, French, English (advance booking recommended)
- Audio guides with historical narratives and traditional music
- Interactive exhibits, historical artifacts, and multimedia presentations
- Photography permitted (additional fee for professional equipment)
- Souvenir shop with local crafts and historical books
- Parking available, accessible facilities for disabled visitors

CULTURAL SIGNIFICANCE:
- Symbol of Algerian resistance and independence struggle
- Represents unity between Arab and Berber populations
- Important pilgrimage site for Algerian patriots
- Educational center for teaching national history
- Venue for cultural events and traditional festivals
- Research center for Ottoman and colonial period studies

YOUR RESPONSE STYLE:
- Provide detailed, comprehensive information (4-6 sentences minimum)
- Include specific historical dates, names, and facts
- Share interesting anecdotes and cultural context
- Explain the significance and importance of topics
- Offer practical visiting advice when relevant
- Use engaging storytelling to bring history to life
- Always encourage deeper exploration and learning
- $languageInstruction

Remember: You are sharing the rich, complex history of this magnificent site. Provide detailed, educational responses that honor the cultural significance while engaging visitors' curiosity!
''';
  }

  static Future<ChatMessage> generateAIResponse(String userMessage, String language) async {
    try {
      final systemPrompt = _getSystemPrompt(language);

      debugPrint('🤖 AI Chat: Sending request to Gemini API');
      debugPrint('🤖 User Message: $userMessage');
      debugPrint('🤖 Language: $language');

      final requestBody = {
        'contents': [
          {
            'parts': [
              {
                'text': '$systemPrompt\n\nUser: $userMessage\n\nAssistant:'
              }
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.8,
          'topK': 50,
          'topP': 0.95,
          'maxOutputTokens': 2048,
          'candidateCount': 1,
        },
        'safetySettings': [
          {
            'category': 'HARM_CATEGORY_HARASSMENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_HATE_SPEECH',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      };

      final response = await http.post(
        Uri.parse('$_baseUrl?key=$_apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      debugPrint('🤖 API Response Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        debugPrint('🤖 API Response Data: $responseData');

        if (responseData['candidates'] != null &&
            responseData['candidates'].isNotEmpty &&
            responseData['candidates'][0]['content'] != null &&
            responseData['candidates'][0]['content']['parts'] != null &&
            responseData['candidates'][0]['content']['parts'].isNotEmpty) {

          String aiResponse = responseData['candidates'][0]['content']['parts'][0]['text'];
          debugPrint('🤖 Raw AI Response: $aiResponse');

          // Clean up the response
          aiResponse = aiResponse.trim();
          if (aiResponse.startsWith('Assistant:')) {
            aiResponse = aiResponse.substring(10).trim();
          }

          debugPrint('🤖 Cleaned AI Response: $aiResponse');

          return ChatMessage(
            id: _uuid.v4(),
            text: aiResponse,
            isUser: false,
            timestamp: DateTime.now(),
            type: MessageType.quickReply,
            quickReplies: _getQuickReplies(language),
          );
        } else {
          debugPrint('🤖 API Response structure invalid');
        }
      } else {
        debugPrint('🤖 Gemini API Error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error calling Gemini API: $e');
    }

    // Fallback response
    return ChatMessage(
      id: _uuid.v4(),
      text: _getFallbackResponse(language),
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.quickReply,
      quickReplies: _getQuickReplies(language),
    );
  }

  static String _getFallbackResponse(String language) {
    switch (language) {
      case 'fr':
        return 'Je suis désolé, je rencontre des difficultés techniques temporaires. Bordj El Mokrani est une magnifique forteresse ottomane construite vers 1774, nommée d\'après Cheikh El Mokrani, le héros de la révolte de 1871 contre le colonialisme français. Cette forteresse historique située dans la province de Bordj Bou Arreridj représente un symbole important de la résistance algérienne et du patrimoine culturel. Puis-je vous aider avec des informations spécifiques sur l\'histoire, l\'architecture, ou les informations de visite ?';
      case 'ar':
        return 'أعتذر، أواجه صعوبات تقنية مؤقتة. برج المقراني هو قلعة عثمانية رائعة بُنيت حوالي عام 1774، سُميت نسبة إلى الشيخ المقراني، بطل ثورة 1871 ضد الاستعمار الفرنسي. تمثل هذه القلعة التاريخية الواقعة في ولاية برج بوعريريج رمزاً مهماً للمقاومة الجزائرية والتراث الثقافي. هل يمكنني مساعدتك بمعلومات محددة حول التاريخ أو العمارة أو معلومات الزيارة؟';
      default:
        return 'I apologize for the temporary technical difficulties. Bordj El Mokrani is a magnificent Ottoman fortress built around 1774, named after Cheikh El Mokrani, the hero of the 1871 revolt against French colonialism. This historic fortress located in Bordj Bou Arreridj Province represents an important symbol of Algerian resistance and cultural heritage. The fortress features classic Ottoman military architecture with thick stone walls, defensive towers, and traditional Islamic decorative elements. Can I help you with specific information about the history, architecture, or visiting details?';
    }
  }

  static List<String> _getQuickReplies(String language) {
    switch (language) {
      case 'fr':
        return [
          'Racontez-moi l\'histoire détaillée de la forteresse',
          'Informations complètes de visite et tarifs',
          'Qui était Cheikh El Mokrani et sa révolte de 1871?',
          'Architecture ottomane et éléments décoratifs',
        ];
      case 'ar':
        return [
          'احكِ لي التاريخ المفصل للقلعة',
          'معلومات الزيارة الكاملة والأسعار',
          'من كان الشيخ المقراني وثورة 1871؟',
          'العمارة العثمانية والعناصر الزخرفية',
        ];
      default:
        return [
          'Tell me the detailed history of the fortress',
          'Complete visiting information and fees',
          'Who was Cheikh El Mokrani and the 1871 revolt?',
          'Ottoman architecture and decorative elements',
        ];
    }
  }

  static ChatMessage getWelcomeMessage(String language) {
    String welcomeText;
    switch (language) {
      case 'fr':
        welcomeText = 'Bienvenue à Bordj El Mokrani ! 🏰 Je suis votre guide virtuel IA expert, passionné par l\'histoire de cette magnifique forteresse ottomane construite vers 1774. Cette forteresse historique, nommée d\'après le légendaire Cheikh El Mokrani qui dirigea la grande révolte de 1871 contre le colonialisme français, représente un symbole puissant de la résistance algérienne et du patrimoine culturel. Je peux vous fournir des informations détaillées sur l\'histoire fascinante, l\'architecture ottomane, les personnages historiques, et toutes les informations pratiques pour votre visite. Comment puis-je vous aider à explorer ce joyau du patrimoine algérien ?';
        break;
      case 'ar':
        welcomeText = 'مرحباً بكم في برج المقراني ! 🏰 أنا دليلكم الافتراضي الخبير بالذكاء الاصطناعي، متحمس لتاريخ هذه القلعة العثمانية الرائعة المبنية حوالي عام 1774. هذه القلعة التاريخية، المسماة نسبة إلى الشيخ المقراني الأسطوري الذي قاد الثورة الكبرى عام 1871 ضد الاستعمار الفرنسي، تمثل رمزاً قوياً للمقاومة الجزائرية والتراث الثقافي. يمكنني تقديم معلومات مفصلة حول التاريخ الرائع والعمارة العثمانية والشخصيات التاريخية وجميع المعلومات العملية لزيارتكم. كيف يمكنني مساعدتكم في استكشاف هذه الجوهرة من التراث الجزائري؟';
        break;
      default:
        welcomeText = 'Welcome to Bordj El Mokrani! 🏰 I\'m your expert AI virtual guide, passionate about the history of this magnificent Ottoman fortress built around 1774. This historic fortress, named after the legendary Cheikh El Mokrani who led the great revolt of 1871 against French colonialism, represents a powerful symbol of Algerian resistance and cultural heritage. I can provide detailed information about the fascinating history, Ottoman architecture, historical figures, and all practical information for your visit. How can I help you explore this jewel of Algerian heritage?';
    }

    return ChatMessage(
      id: _uuid.v4(),
      text: welcomeText,
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.quickReply,
      quickReplies: _getQuickReplies(language),
    );
  }

  static ChatMessage createTypingMessage() {
    return ChatMessage(
      id: _uuid.v4(),
      text: '...',
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.typing,
    );
  }
}
