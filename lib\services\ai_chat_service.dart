import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import '../models/chat_message.dart';
import 'package:uuid/uuid.dart';

class AIChatService {
  static const String _apiKey = 'AIzaSyB70K4h6pR6QrPVpT4FMG4VfrQjb4_W6_I';
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
  static const _uuid = Uuid();

  static String _getSystemPrompt(String language) {
    String languageInstruction = '';
    switch (language) {
      case 'fr':
        languageInstruction = 'Répondez en français.';
        break;
      case 'ar':
        languageInstruction = 'أجب باللغة العربية.';
        break;
      default:
        languageInstruction = 'Respond in English.';
    }

    return '''
You are an expert virtual guide for Bordj <PERSON> (برج المقراني), a historic Ottoman fortress in Algeria. You are passionate, knowledgeable, and helpful.

ABOUT BORDJ EL MOKRANI:
- Built around 1774 during Ottoman rule
- Located in Bordj Bou Arreridj Province, Algeria
- Named after <PERSON><PERSON><PERSON>, leader of the 1871 revolt against French colonialism
- Strategic military fortress and administrative center
- Symbol of Algerian resistance and independence
- Open Saturday-Thursday 8:00 AM - 5:00 PM (closed Fridays)
- Guided tours, audio guides, interactive exhibits available
- Affordable entry fees with student/group discounts

HISTORICAL PERIODS:
- Pre-Islamic: Berber tribes, ancient Numidia
- Roman (146 BCE-430 CE): Roman province
- Islamic (7th-8th century): Arab conquest
- Ottoman (16th-19th century): Fortress construction
- Mokrani Period (19th century): Resistance stronghold
- Colonial (1830-1962): Continued resistance
- Modern (1962-present): Cultural heritage site

YOUR PERSONALITY:
- Enthusiastic about Algerian history and culture
- Warm, welcoming, and professional
- Encouraging visitors to explore and learn
- Respectful of cultural significance
- Helpful with practical information

GUIDELINES:
- Keep responses concise (2-3 sentences typically)
- Provide specific, accurate information
- Suggest related topics when appropriate
- Always encourage visiting
- Stay focused on Bordj El Mokrani topics
- $languageInstruction
- Use appropriate cultural greetings

Remember: You represent this magnificent historical site - maintain dignity while being engaging!
''';
  }

  static Future<ChatMessage> generateAIResponse(String userMessage, String language) async {
    try {
      final systemPrompt = _getSystemPrompt(language);
      
      final requestBody = {
        'contents': [
          {
            'parts': [
              {
                'text': '$systemPrompt\n\nUser: $userMessage\n\nAssistant:'
              }
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.7,
          'topK': 40,
          'topP': 0.95,
          'maxOutputTokens': 1024,
        },
        'safetySettings': [
          {
            'category': 'HARM_CATEGORY_HARASSMENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_HATE_SPEECH',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      };

      final response = await http.post(
        Uri.parse('$_baseUrl?key=$_apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        
        if (responseData['candidates'] != null && 
            responseData['candidates'].isNotEmpty &&
            responseData['candidates'][0]['content'] != null &&
            responseData['candidates'][0]['content']['parts'] != null &&
            responseData['candidates'][0]['content']['parts'].isNotEmpty) {
          
          String aiResponse = responseData['candidates'][0]['content']['parts'][0]['text'];
          
          // Clean up the response
          aiResponse = aiResponse.trim();
          if (aiResponse.startsWith('Assistant:')) {
            aiResponse = aiResponse.substring(10).trim();
          }
          
          return ChatMessage(
            id: _uuid.v4(),
            text: aiResponse,
            isUser: false,
            timestamp: DateTime.now(),
            type: MessageType.quickReply,
            quickReplies: _getQuickReplies(language),
          );
        }
      } else {
        debugPrint('Gemini API Error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Error calling Gemini API: $e');
    }

    // Fallback response
    return ChatMessage(
      id: _uuid.v4(),
      text: _getFallbackResponse(language),
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.quickReply,
      quickReplies: _getQuickReplies(language),
    );
  }

  static String _getFallbackResponse(String language) {
    switch (language) {
      case 'fr':
        return 'Je suis désolé, je rencontre des difficultés techniques. Puis-je vous aider avec des informations sur Bordj El Mokrani ?';
      case 'ar':
        return 'أعتذر، أواجه صعوبات تقنية. هل يمكنني مساعدتك بمعلومات حول برج المقراني؟';
      default:
        return 'I apologize for the technical difficulties. Can I help you with information about Bordj El Mokrani?';
    }
  }

  static List<String> _getQuickReplies(String language) {
    switch (language) {
      case 'fr':
        return [
          'Histoire du fort',
          'Informations de visite',
          'Cheikh El Mokrani',
        ];
      case 'ar':
        return [
          'تاريخ القلعة',
          'معلومات الزيارة',
          'الشيخ المقراني',
        ];
      default:
        return [
          'Fortress History',
          'Visit Information',
          'Cheikh El Mokrani',
        ];
    }
  }

  static ChatMessage getWelcomeMessage(String language) {
    String welcomeText;
    switch (language) {
      case 'fr':
        welcomeText = 'Bienvenue à Bordj El Mokrani ! 🏰 Je suis votre guide virtuel IA. Comment puis-je vous aider à découvrir cette magnifique forteresse historique ?';
        break;
      case 'ar':
        welcomeText = 'مرحباً بكم في برج المقراني ! 🏰 أنا دليلكم الافتراضي بالذكاء الاصطناعي. كيف يمكنني مساعدتكم في اكتشاف هذه القلعة التاريخية الرائعة؟';
        break;
      default:
        welcomeText = 'Welcome to Bordj El Mokrani! 🏰 I\'m your AI virtual guide. How can I help you discover this magnificent historic fortress?';
    }

    return ChatMessage(
      id: _uuid.v4(),
      text: welcomeText,
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.quickReply,
      quickReplies: _getQuickReplies(language),
    );
  }

  static ChatMessage createTypingMessage() {
    return ChatMessage(
      id: _uuid.v4(),
      text: '...',
      isUser: false,
      timestamp: DateTime.now(),
      type: MessageType.typing,
    );
  }
}
